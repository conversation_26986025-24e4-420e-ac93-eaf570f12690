import 'package:firebase_storage/firebase_storage.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../services/firebase_service.dart';
import '../config/anr_config.dart';
import '../../config/firebase_config.dart';
import '../utils/anr_prevention.dart';
import '../../models/activity_model.dart';
import 'optimized_network_service.dart';
import '../../models/document_model.dart';

class DocumentService {
  static DocumentService? _instance;
  static DocumentService get instance => _instance ??= DocumentService._();

  DocumentService._();

  final FirebaseService _firebaseService = FirebaseService.instance;

  // HIGH PRIORITY: Get all documents with pagination and optimization
  Future<List<DocumentModel>> getAllDocuments({
    int? limit,
    DocumentSnapshot? startAfter,
  }) async {
    try {
      final networkService = OptimizedNetworkService.instance;

      // CRITICAL FIX: Use correct collection name and optimized query with proper timeout and error handling
      final querySnapshot = await networkService.executeFirestoreOperation(
        () async {
          Query query = _firebaseService.firestore
              .collection('document-metadata')
              .where('isActive', isEqualTo: true)
              .orderBy('uploadedAt', descending: true);

          if (startAfter != null) {
            query = query.startAfterDocument(startAfter);
          }

          // ENTERPRISE SCALE: Support unlimited queries for enterprise mode
          if (limit != null) {
            query = query.limit(limit);
          } else if (!FirebaseConfig.shouldEnableUnlimitedFiles) {
            // Apply default limit only if not in enterprise mode
            query = query.limit(ANRConfig.defaultPageSize);
          }
          // No limit applied for enterprise mode when limit is null

          return await query.get();
        },
        operationId:
            'get_all_documents_${DateTime.now().millisecondsSinceEpoch}',
        operationName: 'Get All Documents',
        priority: 3,
      );

      if (querySnapshot == null) {
        debugPrint('⚠️ Failed to fetch documents - query timeout');
        return [];
      }

      debugPrint(
        '📊 DocumentService: Query returned ${querySnapshot.docs.length} raw documents',
      );

      // Process documents in batches to prevent ANR
      final documents = <DocumentModel>[];
      await ANRPrevention.batchProcess(
        querySnapshot.docs,
        (doc) async {
          try {
            return DocumentModel.fromFirestore(doc);
          } catch (e) {
            debugPrint('❌ Error parsing document ${doc.id}: $e');
            return null;
          }
        },
        batchSize: ANRConfig.smallBatchSize,
        operationName: 'Document Parsing',
      ).then((results) {
        documents.addAll(
          results.where((doc) => doc != null).cast<DocumentModel>(),
        );
      });

      debugPrint(
        '✅ DocumentService: Successfully parsed ${documents.length} documents',
      );

      if (documents.isEmpty) {
        debugPrint(
          '⚠️ DocumentService: No documents found in Firestore collection',
        );
      } else {
        debugPrint(
          '📋 DocumentService: Latest document: ${documents.first.fileName}',
        );
      }

      return documents;
    } catch (e) {
      debugPrint('❌ Failed to fetch documents: $e');
      return [];
    }
  }

  // Get document by ID using direct lookup only (simplified)
  Future<DocumentModel?> getDocumentById(String documentId) async {
    try {
      debugPrint('🔍 Searching for document with ID: $documentId');

      // Direct lookup only - no complex resolution needed with UUID system
      DocumentSnapshot doc = await _firebaseService.documentsCollection
          .doc(documentId)
          .get();

      if (doc.exists) {
        debugPrint('✅ Document found: $documentId');
        return DocumentModel.fromFirestore(doc);
      }

      debugPrint('⚠️ Document not found: $documentId');
      return null;
    } catch (e) {
      debugPrint('❌ Error in getDocumentById: $e');
      throw Exception('Failed to get document: ${e.toString()}');
    }
  }

  // Add document
  Future<String> addDocument(DocumentModel document) async {
    try {
      DocumentReference docRef = await _firebaseService.documentsCollection.add(
        document.toMap(),
      );

      // Log activity
      await _logActivity(
        document.uploadedBy,
        ActivityType.upload,
        'Document: ${document.fileName}',
      );

      return docRef.id;
    } catch (e) {
      throw Exception('Failed to add document: ${e.toString()}');
    }
  }

  // CRITICAL FIX: Add document without activity logging (for sync operations)
  Future<String> addDocumentSilent(DocumentModel document) async {
    try {
      DocumentReference docRef = await _firebaseService.documentsCollection.add(
        document.toMap(),
      );

      debugPrint(
        '✅ Document added silently (no activity log): ${document.fileName}',
      );
      return docRef.id;
    } catch (e) {
      throw Exception('Failed to add document silently: ${e.toString()}');
    }
  }

  // Update document
  Future<void> updateDocument(DocumentModel document) async {
    try {
      await _firebaseService.documentsCollection
          .doc(document.id)
          .update(document.toMap());
    } catch (e) {
      throw Exception('Failed to update document: ${e.toString()}');
    }
  }

  // Delete document permanently (from both Firestore and Storage)
  Future<void> deleteDocument(String documentId, String deletedBy) async {
    try {
      debugPrint('🗑️ Starting delete operation for document: $documentId');

      // ENHANCED DELETE FIX: Try multiple approaches to find and delete the document
      DocumentModel? document;
      bool documentFoundInFirestore = false;

      // Step 1: Try to get document from Firestore
      try {
        document = await getDocumentById(documentId);
        if (document != null) {
          documentFoundInFirestore = true;
          debugPrint('✅ Document found in Firestore: ${document.fileName}');
        }
      } catch (e) {
        debugPrint('⚠️ Error getting document from Firestore: $e');
      }

      // Step 2: If not found in Firestore, try to find it in Firebase Storage directly
      if (document == null) {
        debugPrint(
          '🔍 Document not found in Firestore, searching Firebase Storage...',
        );
        try {
          // Try to find the file in Firebase Storage using common patterns
          final storagePatterns = [
            'documents/$documentId',
            'documents/$documentId.pdf',
            'documents/$documentId.docx',
            'documents/$documentId.jpg',
            'documents/$documentId.png',
          ];

          for (final pattern in storagePatterns) {
            try {
              final storageRef = _firebaseService.storage.ref().child(pattern);
              final metadata = await storageRef.getMetadata();

              // Create a temporary document model for deletion
              document = DocumentModel(
                id: documentId,
                fileName: metadata.name ?? 'Unknown File',
                fileSize: metadata.size ?? 0,
                fileType: _getFileTypeFromName(metadata.name ?? ''),
                filePath: pattern,
                uploadedBy: 'system',
                uploadedAt: metadata.timeCreated ?? DateTime.now(),
                category: '',
                permissions: [],
                metadata: DocumentMetadata(
                  description: 'Storage-only file',
                  tags: [],
                ),
              );

              debugPrint(
                '✅ Found file in Firebase Storage: ${document.fileName}',
              );
              break;
            } catch (storageError) {
              // Continue to next pattern
              continue;
            }
          }
        } catch (e) {
          debugPrint('⚠️ Error searching Firebase Storage: $e');
        }
      }

      // Step 3: If still not found, create a minimal document for cleanup
      if (document == null) {
        debugPrint(
          '⚠️ Document not found anywhere, creating minimal record for cleanup',
        );
        document = DocumentModel(
          id: documentId,
          fileName: 'Unknown File (ID: $documentId)',
          fileSize: 0,
          fileType: 'unknown',
          filePath: '',
          uploadedBy: deletedBy,
          uploadedAt: DateTime.now(),
          category: '',
          permissions: [],
          metadata: DocumentMetadata(
            description: 'Cleanup operation',
            tags: [],
          ),
        );
      }

      // Step 4: Delete from Firebase Storage if filePath exists
      if (document.filePath.isNotEmpty) {
        try {
          debugPrint(
            '🗑️ Deleting from Firebase Storage: ${document.filePath}',
          );
          Reference storageRef = _firebaseService.storage.ref().child(
            document.filePath,
          );
          await storageRef.delete();
          debugPrint('✅ Successfully deleted from Firebase Storage');
        } catch (storageError) {
          debugPrint(
            '⚠️ Failed to delete from Firebase Storage: $storageError',
          );
          // Continue with Firestore deletion even if storage deletion fails
        }
      }

      // Step 5: Delete from Firestore (only if document was found there)
      if (documentFoundInFirestore) {
        try {
          debugPrint('🗑️ Deleting from Firestore: $documentId');
          await _firebaseService.documentsCollection.doc(documentId).delete();
          debugPrint('✅ Successfully deleted from Firestore');
        } catch (firestoreError) {
          debugPrint('⚠️ Failed to delete from Firestore: $firestoreError');
          // Don't throw here as the document might have been already deleted
        }
      } else {
        debugPrint(
          'ℹ️ Skipping Firestore deletion - document was not found in Firestore',
        );
      }

      // Step 6: Log activity (always log, even for cleanup operations)
      try {
        await _logActivity(
          deletedBy,
          ActivityType.delete,
          'Document: ${document.fileName} (ID: $documentId)',
        );
        debugPrint('✅ Activity logged successfully');
      } catch (activityError) {
        debugPrint('⚠️ Failed to log activity: $activityError');
        // Don't throw here as the main deletion operation succeeded
      }

      debugPrint('✅ Delete operation completed for document: $documentId');
    } catch (e) {
      debugPrint('❌ Delete operation failed for document $documentId: $e');
      throw Exception('Failed to delete document: ${e.toString()}');
    }
  }

  // Helper method to determine file type from filename
  String _getFileTypeFromName(String fileName) {
    if (fileName.isEmpty) return 'unknown';

    final extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'pdf';
      case 'doc':
      case 'docx':
        return 'document';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return 'image';
      case 'mp4':
      case 'avi':
      case 'mov':
        return 'video';
      case 'mp3':
      case 'wav':
        return 'audio';
      default:
        return extension.isNotEmpty ? extension : 'unknown';
    }
  }

  // Delete document from storage only (for cleanup)
  Future<void> deleteDocumentFromStorage(String filePath) async {
    try {
      if (filePath.isNotEmpty) {
        Reference storageRef = _firebaseService.storage.ref().child(filePath);
        await storageRef.delete();
      }
    } catch (e) {
      throw Exception('Failed to delete file from storage: ${e.toString()}');
    }
  }

  // Get documents by category with optimized pagination
  Future<List<DocumentModel>> getDocumentsByCategory(
    String categoryId, {
    int? limit,
    DocumentSnapshot? startAfter,
  }) async {
    try {
      final networkService = OptimizedNetworkService.instance;

      final querySnapshot = await networkService.executeFirestoreOperation(
        () async {
          Query query = _firebaseService.documentsCollection
              .where('isActive', isEqualTo: true) // Add isActive filter
              .where('category', isEqualTo: categoryId)
              .orderBy('uploadedAt', descending: true);

          if (startAfter != null) {
            query = query.startAfterDocument(startAfter);
          }

          // Use pagination to prevent ANR
          final effectiveLimit = limit ?? ANRConfig.defaultPageSize;
          query = query.limit(effectiveLimit);

          return await query.get();
        },
        operationId:
            'get_category_documents_${DateTime.now().millisecondsSinceEpoch}',
        operationName: 'Get Documents by Category',
        priority: 3,
      );

      if (querySnapshot == null) {
        debugPrint('⚠️ Failed to fetch category documents - query timeout');
        return [];
      }

      return querySnapshot.docs
          .map((doc) => DocumentModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      debugPrint('❌ Error getting documents by category: $e');
      throw Exception('Failed to get documents by category: ${e.toString()}');
    }
  }

  // Get documents by user
  Future<List<DocumentModel>> getDocumentsByUser(String userId) async {
    try {
      QuerySnapshot querySnapshot = await _firebaseService.documentsCollection
          .where('uploadedBy', isEqualTo: userId)
          .orderBy('uploadedAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => DocumentModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get documents by user: ${e.toString()}');
    }
  }

  // Update document category
  Future<void> updateDocumentCategory(
    String documentId,
    String newCategoryId,
  ) async {
    try {
      await _firebaseService.documentsCollection.doc(documentId).update({
        'category': newCategoryId,
      });
    } catch (e) {
      throw Exception('Failed to update document category: ${e.toString()}');
    }
  }

  // Status update method removed since status management is removed

  // Search documents with optimized pagination and ANR prevention
  Future<List<DocumentModel>> searchDocuments(
    String query, {
    int? limit,
    DocumentSnapshot? startAfter,
  }) async {
    try {
      final networkService = OptimizedNetworkService.instance;

      final querySnapshot = await networkService.executeFirestoreOperation(
        () async {
          Query searchQuery = _firebaseService.documentsCollection
              .where(
                'isActive',
                isEqualTo: true,
              ) // Only search active documents
              .where('fileName', isGreaterThanOrEqualTo: query)
              .where('fileName', isLessThanOrEqualTo: '$query\uf8ff')
              .orderBy('fileName')
              .orderBy('uploadedAt', descending: true);

          if (startAfter != null) {
            searchQuery = searchQuery.startAfterDocument(startAfter);
          }

          // Use pagination to prevent ANR
          final effectiveLimit = limit ?? ANRConfig.defaultPageSize;
          searchQuery = searchQuery.limit(effectiveLimit);

          return await searchQuery.get();
        },
        operationId:
            'search_documents_${DateTime.now().millisecondsSinceEpoch}',
        operationName: 'Search Documents',
        priority: 3,
      );

      if (querySnapshot == null) {
        debugPrint('⚠️ Failed to search documents - query timeout');
        return [];
      }

      return querySnapshot.docs
          .map((doc) => DocumentModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      debugPrint('❌ Error searching documents: $e');
      throw Exception('Failed to search documents: ${e.toString()}');
    }
  }

  // Get recent documents with optimized query and ANR prevention
  Future<List<DocumentModel>> getRecentDocuments({
    int limit = 10,
    DocumentSnapshot? startAfter,
  }) async {
    try {
      final networkService = OptimizedNetworkService.instance;

      final querySnapshot = await networkService.executeFirestoreOperation(
        () async {
          Query query = _firebaseService.documentsCollection
              .where('isActive', isEqualTo: true) // Only get active documents
              .orderBy('uploadedAt', descending: true);

          if (startAfter != null) {
            query = query.startAfterDocument(startAfter);
          }

          // ENTERPRISE SCALE: Support unlimited queries for enterprise mode
          if (FirebaseConfig.shouldEnableUnlimitedFiles &&
              limit > ANRConfig.maxItemsPerPage) {
            // Allow unlimited queries for enterprise mode
            query = query.limit(limit);
          } else {
            // Apply safe limit for standard mode
            final effectiveLimit = limit > ANRConfig.maxItemsPerPage
                ? ANRConfig.maxItemsPerPage
                : limit;
            query = query.limit(effectiveLimit);
          }

          return await query.get();
        },
        operationId:
            'get_recent_documents_${DateTime.now().millisecondsSinceEpoch}',
        operationName: 'Get Recent Documents',
        priority: 3,
      );

      if (querySnapshot == null) {
        debugPrint('⚠️ Failed to fetch recent documents - query timeout');
        return [];
      }

      return querySnapshot.docs
          .map((doc) => DocumentModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      debugPrint('❌ Error getting recent documents: $e');
      throw Exception('Failed to get recent documents: ${e.toString()}');
    }
  }

  // Log activity
  Future<void> _logActivity(
    String userId,
    ActivityType action,
    String resource,
  ) async {
    try {
      ActivityModel activity = ActivityModel(
        id: '',
        userId: userId,
        action: action.value,
        resource: resource,
        timestamp: DateTime.now(),
        details: {'userAgent': 'Flutter App', 'platform': 'Mobile'},
      );

      await _firebaseService.activitiesCollection.add(activity.toMap());
    } catch (e) {
      // Don't throw error for activity logging
      // Failed to log activity, but continue execution
      debugPrint('Failed to log activity: $e');
    }
  }
}
